import os
import re
from datetime import datetime


class MDXStorage:
    def __init__(self, storage_dir="mdx_storage"):
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)

    def clean_mdx(self, mdx_content: str) -> str:
        """Clean MDX content by removing trailing spaces, extra newlines, and unwanted characters."""
        # Remove trailing spaces
        mdx_content = re.sub(r"[ \t]+$", "", mdx_content, flags=re.MULTILINE)
        # Normalize line breaks
        mdx_content = mdx_content.replace("\r\n", "\n").strip()
        # Optionally remove multiple blank lines
        mdx_content = re.sub(r"\n{3,}", "\n\n", mdx_content)
        return mdx_content

    def save(self, mdx_content: str, filename: str = None) -> str:
        """Clean and save MDX to a file."""
        cleaned_content = self.clean_mdx(mdx_content)

        if filename is None:
            # Default filename with timestamp
            filename = datetime.now().strftime("%Y%m%d_%H%M%S") + ".mdx"

        filepath = os.path.join(self.storage_dir, filename)

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(cleaned_content)

        return filepath

    def read(self, filename: str) -> str:
        """Read MDX file content."""
        filepath = os.path.join(self.storage_dir, filename)
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"MDX file not found: {filename}")

        with open(filepath, "r", encoding="utf-8") as f:
            return f.read()
