from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain.prompts import PromptTemplate


class GenerateRoadmap:
    def __init__(
        self,
        api_key: str = "AIzaSyAuS3V_W8jvVtdJcz29QNbU6ovfBtz8WHw",
        model: str = "gemini-1.5-flash",
    ):
        self.llm = ChatGoogleGenerativeAI(model=model, google_api_key=api_key)

    def generate(self, payload: dict) -> str:
        new_payload = {
            "age": payload["age"],
            "field": payload["currentField"],
            "role": payload["currentRole"],
            "days_per_week": payload["daysPerWeek"],
            "hours_per_day": payload["hoursPerDay"],
            "current_knowledge": payload["knowledgeDescription"],
            "knowledge_level": payload["knowledgeLevel"],
            "topic": payload["topic"],
            "reason": payload["topicReason"],
            "duration": payload["totalDuration"],
            "start_date": "2024-10-01",
        }

        prompt = PromptTemplate(
            input_variables=[
                "age",
                "field",
                "role",
                "days_per_week",
                "hours_per_day",
                "current_knowledge",
                "knowledge_level",
                "topic",
                "reason",
                "duration",
                "start_date",
            ],
            template=prompt_template,
        )

        formatted_prompt = prompt.format(**new_payload)
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=formatted_prompt),
        ]
        response = self.llm.invoke(messages)

        return response.content


prompt_template = """
Here is the student's learning info:
Age: {age}  
Field/Industry: {field}  
Current Role: {role}  
Days per Week: {days_per_week}  
Hours per Day: {hours_per_day}  
Describe What You Know: {current_knowledge}  
Knowledge Level (0–10): {knowledge_level}  
Topic to Learn: {topic}  
Reason for Learning: {reason}  
Total Duration (weeks/months): {duration}  

TASK:
1. Create a **roadmap** for learning the topic within the given total duration.
2. Break the roadmap into a **course structure** with clear modules or sections.
3. Assign each module to specific **days** based on the student's availability (`daysPerWeek` × `hoursPerDay`).
4. Within each day, split content into **topics** based on actual complexity and estimated learning time (not fixed time rules).
5. If a topic is too large for one day, split it into sequential parts (e.g., "Part 1", "Part 2") to ensure complete coverage.
6. Ensure topics build progressively from beginner-friendly foundations to advanced mastery.
7. Each topic must include:
   - `topic_number` (sequential across the whole plan)
   - `title` (clear and descriptive)
   - `description` (short summary)
   - `estimated_time_hours` (realistic time needed for the topic)
   - `content` (2000–4000 words, **mdx blog-style format** with real-world examples, analogies, applied exercises, and contextual explanations)
   - `resources` (1–5 credible references: links, books, videos)
8. Each day must include:
   - `day` (number)
   - `total_time_hours` (sum of estimated time for all topics that day)
   - `topics` (list of topic objects)


"""
system_prompt = """SYSTEM:
You are a distinguished university professor and a top-tier technical blogger, specializing in computer science and AI.  
Your task is to create a **detailed, example-rich learning plan** that teaches the given topic in a structured, engaging, and academically rigorous way.

TOPIC DISTRIBUTION RULES:
- Base topic duration on **content complexity** and **depth**, not a fixed minutes-per-topic rule.
- Large topics should be **split into parts** so each is manageable and deeply covered.
- Each day's `total_time_hours` should reflect the sum of all topics assigned for that day.

WRITING STYLE:
- Combine **academic depth** with a conversational, blog-style tone.
- Use **real-world analogies**, **contextual insights**, and **hands-on examples**.
- Format `content` in **mdx** with:
  - Headings & subheadings
  - Lists
  - Tables (if relevant)
  - Highlighted key points
- End each topic with **references** or **practical exercises**.

SCHEMA:
{{
  "plan": [
    {{
      "day": number,
      "total_time_hours": number,
      "topics": [
        {{
          "topic_number": number,
          "title": string,
          "description": string,
          "estimated_time_hours": number,
          "content": "string(mdx format)",
          "resources": [string]
        }}
      ]
    }}
  ]
}}
"""
