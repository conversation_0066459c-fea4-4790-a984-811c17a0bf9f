from fastapi import APIRouter, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.db.connection import get_db
from app.core.roadmap.generate_roadmap import GenerateRoadmap
import json
from typing import Any
from fastapi import Depends
import re


router = APIRouter()


class Roadmap(BaseModel):
    age: str
    currentField: str
    currentRole: str
    daysPerWeek: int
    hoursPerDay: int
    knowledgeDescription: str
    knowledgeLevel: int
    topic: str
    topicReason: str
    totalDuration: str


class RoadmapResponse(BaseModel):
    success: bool
    message: str
    data: Any = None


def clean_json_string(s):
    """Clean JSON string by removing markdown formatting and fixing newlines"""
    # Remove triple backticks and 'json' label
    s = s.strip("`")
    s = re.sub(r"^json\s*", "", s, flags=re.MULTILINE)
    # Fix escaped newlines in content fields
    s = re.sub(
        r'("content":\s*")(.*?)("(?=\s*,\s*"resources"))',
        lambda m: m.group(1) + m.group(2).replace("\n", "\\n") + m.group(3),
        s,
        flags=re.DOTALL,
    )
    return s


@router.post("/roadmap", response_model=RoadmapResponse)
async def create_roadmap(payload: Roadmap):
    try:
        # Convert payload to dict for GenerateRoadmap
        roadmap_data = payload.model_dump()

        # Generate roadmap using LLM
        # generator = GenerateRoadmap()
        # response = generator.generate(roadmap_data)

        # Clean and parse JSON response
        cleaned = clean_json_string(llm_response)
        llm_dict = json.loads(cleaned)

        if not llm_dict:
            raise ValueError("GenerateRoadmap returned empty data")

        return RoadmapResponse(success=True, message="Roadmap generated successfully")

    except json.JSONDecodeError as e:
        raise HTTPException(
            status_code=500, detail=f"Invalid JSON response from LLM: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating roadmap: {str(e)}"
        )


class GetRoadmapResponse(BaseModel):
    success: bool
    message: str
    data: Any = None


class GetRoadmapPayload(BaseModel):
    id: int


@router.get("/roadmap/{id}", response_model=GetRoadmapResponse)
async def get_roadmap(id: int, db: Session = Depends(get_db)):
    try:

        get_current_roadmap = [item for item in lists if item["id"] == id]
        print(get_current_roadmap)
        if not get_current_roadmap:
            raise ValueError("GenerateRoadmap returned empty data")

        return GetRoadmapResponse(success=True, message="Roadmap fetched successfully", data=get_current_roadmap[0]["data"])
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching roadmap: {str(e)}")
    
@router.get("/roadmap/{id}/day/{day}", response_model=GetRoadmapResponse)
async def get_day_topics(id: int, day: int):
    try:
        get_current_roadmap = [item for item in lists if item["id"] == id]
        if not get_current_roadmap:
            raise ValueError("GenerateRoadmap returned empty data")
        day_topics = [
            day_topics
            for day_topics in get_current_roadmap[0]["data"]
            if day_topics["day"] == day
        ]
        print(day_topics)
        return GetRoadmapResponse(success=True, message="Day topics fetched successfully", data=day_topics[0]["topics"])
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching day topics: {str(e)}")













llm_response = '```json\n{\n  "plan": [\n    {\n      "day": 1,\n      "total_time_hours": 3,\n      "topics": [\n        {\n          "topic_number": 1,\n          "title": "Python Fundamentals: Setting up your Environment and First Programs",\n          "description": "Installing Python, choosing an IDE, and writing your first \'Hello, World!\' program.",\n          "estimated_time_hours": 1,\n          "content": "# Python Fundamentals: Setting up your Environment and First Programs\\n\\nThis module lays the groundwork for your Python journey.  We\'ll cover the essential steps to get your development environment ready and write your very first Python program.  Think of this as building the foundation of a house – you need a strong base before you can build the walls and roof.\\n\\n## 1.1 Installing Python\\n\\nFirst things first: you need Python installed on your machine.  Head over to the official Python website ([https://www.python.org/downloads/](https://www.python.org/downloads/)) and download the latest version for your operating system (Windows, macOS, or Linux).  During installation, make sure to check the box that adds Python to your system\'s PATH. This allows you to run Python from your terminal or command prompt without specifying the full path to the executable.\\n\\n## 1.2 Choosing an IDE\\n\\nAn Integrated Development Environment (IDE) is like your workshop for building software.  It provides a convenient environment for writing, running, and debugging your code.  Popular choices for Python include:\\n\\n* **VS Code:** A free, lightweight, and highly customizable editor with excellent Python support through extensions.\\n* **PyCharm:** A powerful IDE with a lot of features, available in both free (Community Edition) and paid (Professional Edition) versions.\\n* **Thonny:** A beginner-friendly IDE, perfect for learning the basics.\\n\\nFor this course, we\'ll primarily use VS Code due to its flexibility and popularity. You can download it from [https://code.visualstudio.com/](https://code.visualstudio.com/).\\n\\n## 1.3 Your First Python Program: Hello, World!\\n\\nLet\'s write our first Python program. This classic program simply prints the words \\"Hello, World!\\" to the console.  It\'s a tradition in programming, a rite of passage, and a great way to test your setup.\\n\\nOpen your chosen IDE and create a new file (e.g., `hello.py`).  Type the following code:\\n\\n```python\\nprint(\\"Hello, World!\\")\\n```\\n\\nSave the file and run it.  If everything is set up correctly, you should see \\"Hello, World!\\" printed in your console.  Congratulations! You\'ve successfully written your first Python program.\\n\\n## 1.4 Key Concepts Introduced:\\n\\n* **Python Installation:**  Ensuring Python is correctly installed and accessible from your system.\\n* **IDE Selection:** Choosing a suitable IDE based on your needs and preferences.\\n* **`print()` Function:** The fundamental function for displaying output in Python.\\n* **String Literals:**  Text enclosed in double quotes (\\"\\").\\n\\n## Exercises:\\n\\n1.  Install Python and your chosen IDE.\\n2.  Run the \\"Hello, World!\\" program.\\n3.  Experiment with printing different messages using the `print()` function.  Try printing your name, your favorite number, or a short sentence.\\n4.  Research different IDEs and their features.  Try installing and using a different IDE.\\n\\n",\n          "resources": [\n            "https://www.python.org/",\n            "https://code.visualstudio.com/",\n            "https://www.jetbrains.com/pycharm/",\n            "https://thonny.org/"\n          ]\n        },\n        {\n          "topic_number": 2,\n          "title": "Data Types and Variables",\n          "description": "Understanding fundamental data types (integers, floats, strings, booleans) and how to use variables to store data.",\n          "estimated_time_hours": 1,\n          "content": "# Data Types and Variables\\n\\nIn this module, we explore the building blocks of any program: data types and variables.  Think of data types as containers that hold different kinds of information, and variables as named labels for these containers.  Understanding these concepts is crucial for writing effective and efficient Python code.\\n\\n## 2.1 Fundamental Data Types\\n\\nPython has several built-in data types.  Let\'s examine some of the most common ones:\\n\\n* **Integers (`int`):** Whole numbers (e.g., 10, -5, 0).\\n* **Floating-point numbers (`float`):** Numbers with decimal points (e.g., 3.14, -2.5, 0.0).\\n* **Strings (`str`):** Sequences of characters (e.g., \\"Hello\\", \'Python\', \\"123\\").\\n* **Booleans (`bool`):** Represent truth values (`True` or `False`).\\n\\n```python\\ninteger_var = 10\\nfloat_var = 3.14\\nstring_var = \\"Hello, world!\\"\\nboolean_var = True\\n```\\n\\n## 2.2 Variables\\n\\nVariables are used to store data.  They are essentially named containers that hold values.  In Python, you don\'t need to explicitly declare the data type of a variable; Python infers it based on the assigned value.\\n\\n```python\\nname = \\"Alice\\"  # String variable\\nage = 30       # Integer variable\\nheight = 5.8    # Float variable\\nis_student = True # Boolean variable\\n```\\n\\n## 2.3 Variable Naming Conventions\\n\\n* Use descriptive names (e.g., `user_name` instead of `u`).\\n* Start with a letter or underscore (_).\\n* Use lowercase letters with underscores to separate words (snake_case).\\n* Avoid using reserved keywords (e.g., `if`, `else`, `for`).\\n\\n## 2.4 Type Conversion\\n\\nSometimes, you need to convert data from one type to another.  Python provides built-in functions for this:\\n\\n```python\\nnumber_str = \\"10\\"\\nnumber_int = int(number_str) # Convert string to integer\\nprint(number_int + 5) # Output: 15\\n```\\n\\n## Exercises:\\n\\n1.  Declare variables of different data types and print their values.\\n2.  Perform type conversion between integers and strings.\\n3.  Write a program that takes user input (name and age) and prints a greeting message.\\n\\n",\n          "resources": [\n            "https://docs.python.org/3/tutorial/introduction.html",\n            "https://realpython.com/python-data-types/"\n          ]\n        }\n      ]\n    },\n    {\n      "day": 2,\n      "total_time_hours": 3,\n      "topics": [\n        {\n          "topic_number": 3,\n          "title": "Operators and Expressions",\n          "description": "Mastering arithmetic, comparison, logical, and assignment operators; understanding operator precedence.",\n          "estimated_time_hours": 1.5,\n          "content": "# Operators and Expressions\\n\\nOperators are symbols that perform operations on operands (variables or values).  Expressions are combinations of operators and operands that evaluate to a value.  Understanding operators and expressions is fundamental to writing any program that involves calculations or comparisons.\\n\\n## 3.1 Arithmetic Operators\\n\\nThese operators perform basic arithmetic operations:\\n\\n| Operator | Description       | Example     |\n| --------- | ----------------- | ----------- |\n| `+`       | Addition           | `x + y`     |\n| `-`       | Subtraction        | `x - y`     |\n| `*`       | Multiplication     | `x * y`     |\n| `/`       | Division           | `x / y`     |\n| `//`      | Floor Division     | `x // y`    |\n| `%`       | Modulus (remainder)| `x % y`     |\n| `**`      | Exponentiation     | `x ** y`    |\\n\\n## 3.2 Comparison Operators\\n\\nThese operators compare two values and return a boolean result (`True` or `False`):\\n\\n| Operator | Description          | Example     |\n| --------- | -------------------- | ----------- |\n| `==`      | Equal to             | `x == y`    |\n| `!=`      | Not equal to         | `x != y`    |\n| `>`       | Greater than         | `x > y`     |\n| `<`       | Less than            | `x < y`     |\n| `>=`      | Greater than or equal | `x >= y`    |\n| `<=`      | Less than or equal   | `x <= y`    |\\n\\n## 3.3 Logical Operators\\n\\nThese operators combine boolean expressions:\\n\\n| Operator | Description     | Example       |\n| --------- | ---------------- | -------------- |\n| `and`     | Logical AND      | `x and y`     |\n| `or`      | Logical OR       | `x or y`      |\n| `not`     | Logical NOT      | `not x`       |\\n\\n## 3.4 Assignment Operators\\n\\nThese operators assign values to variables:\\n\\n| Operator | Description          | Example     |\n| --------- | -------------------- | ----------- |\n| `=`       | Assignment           | `x = 10`    |\n| `+=`      | Add and assign      | `x += 5`    |\n| `-=`      | Subtract and assign | `x -= 3`    |\n| `*=`      | Multiply and assign | `x *= 2`    |\n| `/=`      | Divide and assign   | `x /= 4`    |\\n\\n## 3.5 Operator Precedence\\n\\nOperator precedence determines the order in which operations are performed.  Parentheses `()` can be used to override precedence.\\n\\n## Exercises:\\n\\n1.  Write a program that calculates the area of a circle.\\n2.  Write a program that determines if a number is even or odd.\\n3.  Write a program that checks if a user is eligible to vote (age >= 18).\\n4.  Write a program that converts Celsius to Fahrenheit.\\n\\n",\n          "resources": [\n            "https://docs.python.org/3/reference/expressions.html",\n            "https://realpython.com/python-operators-expressions/"\n          ]\n        },\n        {\n          "topic_number": 4,\n          "title": "Control Flow: Conditional Statements",\n          "description": "Using `if`, `elif`, and `else` statements to control the flow of execution based on conditions.",\n          "estimated_time_hours": 1.5,\n          "content": "# Control Flow: Conditional Statements\\n\\nControl flow statements determine the order in which statements are executed in a program.  Conditional statements, using `if`, `elif`, and `else`, allow you to execute different blocks of code based on whether certain conditions are true or false.  This is fundamental to creating programs that can make decisions and respond to different situations.\\n\\n## 4.1 `if` Statement\\n\\nThe simplest conditional statement.  If the condition is true, the code block within the `if` statement is executed.\\n\\n```python\\nage = 20\\nif age >= 18:\\n    print(\\"Eligible to vote\\")\\n```\\n\\n## 4.2 `if-else` Statement\\n\\nProvides two possible execution paths.  If the condition is true, the `if` block is executed; otherwise, the `else` block is executed.\\n\\n```python\\nage = 15\\nif age >= 18:\\n    print(\\"Eligible to vote\\")\\nelse:\\n    print(\\"Not eligible to vote\\")\\n```\\n\\n## 4.3 `if-elif-else` Statement\\n\\nHandles multiple conditions.  The conditions are evaluated sequentially.  If a condition is true, its corresponding block is executed, and the rest are skipped.  The `else` block (optional) is executed only if none of the conditions are true.\\n\\n```python\\ngrade = 85\\nif grade >= 90:\\n    print(\\"A\\")\\nelif grade >= 80:\\n    print(\\"B\\")\\nelif grade >= 70:\\n    print(\\"C\\")\\nelse:\\n    print(\\"F\\")\\n```\\n\\n## 4.4 Nested Conditional Statements\\n\\nYou can place conditional statements inside other conditional statements to create more complex logic.\\n\\n```python\\nage = 25\\nincome = 50000\\nif age >= 18:\\n    if income >= 40000:\\n        print(\\"Eligible for loan\\")\\n    else:\\n        print(\\"Income too low for loan\\")\\nelse:\\n    print(\\"Too young for loan\\")\\n```\\n\\n## Exercises:\\n\\n1. Write a program that determines if a number is positive, negative, or zero.\\n2. Write a program that determines the largest of three numbers.\\n3. Write a program that calculates the grade based on a score (using if-elif-else).\\n4. Create a program that simulates a simple calculator (addition, subtraction, multiplication, division) based on user input.\\n\\n",\n          "resources": [\n            "https://docs.python.org/3/tutorial/controlflow.html",\n            "https://realpython.com/python-conditional-statements/"\n          ]\n        }\n      ]\n    },\n    {\n      "day": 3,\n      "total_time_hours": 3,\n      "topics": [\n        {\n          "topic_number": 5,\n          "title": "Loops: `for` and `while` loops",\n          "description": "Iterating over sequences and repeating code blocks using `for` and `while` loops.",\n          "estimated_time_hours": 1.5,\n          "content": "# Loops: `for` and `while` loops\\n\\nLoops are essential for automating repetitive tasks.  Python offers two main types of loops: `for` loops and `while` loops.  `for` loops are generally used for iterating over a sequence (like a list or string), while `while` loops are used for repeating a block of code as long as a condition is true.\\n\\n## 5.1 `for` Loops\\n\\n`for` loops iterate over each item in a sequence:\\n\\n```python\\nfruits = [\\"apple\\", \\"banana\\", \\"cherry\\"]\\nfor fruit in fruits:\\n    print(fruit)\\n```\\n\\nYou can also use `for` loops with `range()` to iterate a specific number of times:\\n\\n```python\\nfor i in range(5):\\n    print(i) # Prints 0, 1, 2, 3, 4\\n```\\n\\n## 5.2 `while` Loops\\n\\n`while` loops execute a block of code as long as a condition is true:\\n\\n```python\\ncount = 0\\nwhile count < 5:\\n    print(count)\\n    count += 1\\n```\\n\\n## 5.3 Loop Control Statements\\n\\n* **`break`:** Terminates the loop prematurely.\\n* **`continue`:** Skips the current iteration and proceeds to the next.\\n\\n```python\\nfor i in range(10):\\n    if i == 5:\\n        break  # Exit the loop when i is 5\\n    print(i)\\n\\nfor i in range(10):\\n    if i == 5:\\n        continue # Skip the iteration when i is 5\\n    print(i)\\n```\\n\\n## 5.4 Nested Loops\\n\\nYou can place loops inside other loops to create more complex iterations.  This is often used for processing two-dimensional data structures (like matrices or tables).\\n\\n```python\\nfor i in range(3):\\n    for j in range(3):\\n        print(f\\"({i}, {j})\\")\\n```\\n\\n## Exercises:\\n\\n1. Write a program that prints the numbers from 1 to 10 using a `for` loop.\\n2. Write a program that calculates the sum of numbers from 1 to 100 using a `while` loop.\\n3. Write a program that prints the even numbers from 1 to 20 using a `for` loop and a conditional statement.\\n4. Write a program that simulates a simple guessing game using a `while` loop and a `break` statement.\\n5. Write a program to print multiplication table of a given number using nested loops.\\n\\n",\n          "resources": [\n            "https://docs.python.org/3/tutorial/controlflow.html#for-statements",\n            "https://docs.python.org/3/tutorial/controlflow.html#while-statements",\n            "https://realpython.com/python-for-loop/"\n          ]\n        },\n        {\n          "topic_number": 6,\n          "title": "Data Structures: Lists and Tuples",\n          "description": "Working with lists (mutable ordered sequences) and tuples (immutable ordered sequences).",\n          "estimated_time_hours": 1.5,\n          "content": "# Data Structures: Lists and Tuples\\n\\nData structures are ways of organizing and storing data efficiently.  Lists and tuples are two fundamental data structures in Python that are used to store sequences of items.  They are similar in that they are ordered sequences, but they differ in their mutability (ability to be changed after creation).\\n\\n## 6.1 Lists\\n\\nLists are mutable, ordered sequences of items.  They are defined using square brackets `[]`.\\n\\n```python\\nmy_list = [1, 2, 3, \\"apple\\", \\"banana\\"]\\n```\\n\\n**List Operations:**\\n\\n* **Access elements:** `my_list[0]` (accesses the first element)\\n* **Slicing:** `my_list[1:3]` (accesses elements from index 1 to 2)\\n* **Append:** `my_list.append(4)` (adds 4 to the end)\\n* **Insert:** `my_list.insert(2, \\"orange\\")` (inserts \\"orange\\" at index 2)\\n* **Remove:** `my_list.remove(\\"apple\\")` (removes the first occurrence of \\"apple\\")\\n* **Pop:** `my_list.pop()` (removes and returns the last element)\\n* **Length:** `len(my_list)` (returns the number of elements)\\n\\n## 6.2 Tuples\\n\\nTuples are immutable, ordered sequences of items.  They are defined using parentheses `()`.\\n\\n```python\\nmy_tuple = (1, 2, 3, \\"apple\\", \\"banana\\")\\n```\\n\\nBecause tuples are immutable, you cannot modify them after creation (you can\'t append, insert, remove elements).  However, you can still access elements using indexing and slicing, just like with lists.\\n\\n## 6.3 Choosing Between Lists and Tuples\\n\\n* Use lists when you need a mutable sequence (you\'ll need to add, remove, or change elements).\\n* Use tuples when you need an immutable sequence (the data should not be changed after creation).  Tuples are generally slightly more memory-efficient than lists.\\n\\n## Exercises:\\n\\n1. Create a list of your favorite colors and print each color.\\n2. Create a tuple of your favorite fruits and print the number of fruits.\\n3. Create a list of numbers, sort it in ascending order, and print the sorted list.\\n4. Create a list of strings, reverse it, and print the reversed list.\\n5.  Create a tuple containing a list.  Try modifying the list within the tuple. What happens? Explain why.\\n\\n",\n          "resources": [\n            "https://docs.python.org/3/tutorial/datastructures.html",\n            "https://realpython.com/python-lists-tuples/"\n          ]\n        }\n      ]\n    }\n  ]\n}\n```'


lists = [
    {
        "id": 1,
        "data": [
            {
                "day": 1,
                "total_time_hours": 3,
                "topics": [
                    {
                        "topic_number": 1,
                        "title": "Python Fundamentals: Setting up your Environment and First Programs",
                        "description": "Installing Python, choosing an IDE, and writing your first 'Hello, World!' program.",
                        "estimated_time_hours": 1,
                        "content": '# Python Fundamentals: Setting up your Environment and First Programs\n\nThis module lays the groundwork for your Python journey.  We\'ll cover the essential steps to get your development environment ready and write your very first Python program.  Think of this as building the foundation of a house – you need a strong base before you can build the walls and roof.\n\n## 1.1 Installing Python\n\nFirst things first: you need Python installed on your machine.  Head over to the official Python website ([https://www.python.org/downloads/](https://www.python.org/downloads/)) and download the latest version for your operating system (Windows, macOS, or Linux).  During installation, make sure to check the box that adds Python to your system\'s PATH. This allows you to run Python from your terminal or command prompt without specifying the full path to the executable.\n\n## 1.2 Choosing an IDE\n\nAn Integrated Development Environment (IDE) is like your workshop for building software.  It provides a convenient environment for writing, running, and debugging your code.  Popular choices for Python include:\n\n* **VS Code:** A free, lightweight, and highly customizable editor with excellent Python support through extensions.\n* **PyCharm:** A powerful IDE with a lot of features, available in both free (Community Edition) and paid (Professional Edition) versions.\n* **Thonny:** A beginner-friendly IDE, perfect for learning the basics.\n\nFor this course, we\'ll primarily use VS Code due to its flexibility and popularity. You can download it from [https://code.visualstudio.com/](https://code.visualstudio.com/).\n\n## 1.3 Your First Python Program: Hello, World!\n\nLet\'s write our first Python program. This classic program simply prints the words "Hello, World!" to the console.  It\'s a tradition in programming, a rite of passage, and a great way to test your setup.\n\nOpen your chosen IDE and create a new file (e.g., `hello.py`).  Type the following code:\n\n```python\nprint("Hello, World!")\n```\n\nSave the file and run it.  If everything is set up correctly, you should see "Hello, World!" printed in your console.  Congratulations! You\'ve successfully written your first Python program.\n\n## 1.4 Key Concepts Introduced:\n\n* **Python Installation:**  Ensuring Python is correctly installed and accessible from your system.\n* **IDE Selection:** Choosing a suitable IDE based on your needs and preferences.\n* **`print()` Function:** The fundamental function for displaying output in Python.\n* **String Literals:**  Text enclosed in double quotes ("").\n\n## Exercises:\n\n1.  Install Python and your chosen IDE.\n2.  Run the "Hello, World!" program.\n3.  Experiment with printing different messages using the `print()` function.  Try printing your name, your favorite number, or a short sentence.\n4.  Research different IDEs and their features.  Try installing and using a different IDE.\n\n',
                        "resources": [
                            "https://www.python.org/",
                            "https://code.visualstudio.com/",
                            "https://www.jetbrains.com/pycharm/",
                            "https://thonny.org/",
                        ],
                    },
                    {
                        "topic_number": 2,
                        "title": "Data Types and Variables",
                        "description": "Understanding fundamental data types (integers, floats, strings, booleans) and how to use variables to store data.",
                        "estimated_time_hours": 1,
                        "content": '# Data Types and Variables\n\nIn this module, we explore the building blocks of any program: data types and variables.  Think of data types as containers that hold different kinds of information, and variables as named labels for these containers.  Understanding these concepts is crucial for writing effective and efficient Python code.\n\n## 2.1 Fundamental Data Types\n\nPython has several built-in data types.  Let\'s examine some of the most common ones:\n\n* **Integers (`int`):** Whole numbers (e.g., 10, -5, 0).\n* **Floating-point numbers (`float`):** Numbers with decimal points (e.g., 3.14, -2.5, 0.0).\n* **Strings (`str`):** Sequences of characters (e.g., "Hello", \'Python\', "123").\n* **Booleans (`bool`):** Represent truth values (`True` or `False`).\n\n```python\ninteger_var = 10\nfloat_var = 3.14\nstring_var = "Hello, world!"\nboolean_var = True\n```\n\n## 2.2 Variables\n\nVariables are used to store data.  They are essentially named containers that hold values.  In Python, you don\'t need to explicitly declare the data type of a variable; Python infers it based on the assigned value.\n\n```python\nname = "Alice"  # String variable\nage = 30       # Integer variable\nheight = 5.8    # Float variable\nis_student = True # Boolean variable\n```\n\n## 2.3 Variable Naming Conventions\n\n* Use descriptive names (e.g., `user_name` instead of `u`).\n* Start with a letter or underscore (_).\n* Use lowercase letters with underscores to separate words (snake_case).\n* Avoid using reserved keywords (e.g., `if`, `else`, `for`).\n\n## 2.4 Type Conversion\n\nSometimes, you need to convert data from one type to another.  Python provides built-in functions for this:\n\n```python\nnumber_str = "10"\nnumber_int = int(number_str) # Convert string to integer\nprint(number_int + 5) # Output: 15\n```\n\n## Exercises:\n\n1.  Declare variables of different data types and print their values.\n2.  Perform type conversion between integers and strings.\n3.  Write a program that takes user input (name and age) and prints a greeting message.\n\n',
                        "resources": [
                            "https://docs.python.org/3/tutorial/introduction.html",
                            "https://realpython.com/python-data-types/",
                        ],
                    },
                ],
            },
            {
                "day": 2,
                "total_time_hours": 3,
                "topics": [
                    {
                        "topic_number": 3,
                        "title": "Operators and Expressions",
                        "description": "Mastering arithmetic, comparison, logical, and assignment operators; understanding operator precedence.",
                        "estimated_time_hours": 1.5,
                        "content": "# Operators and Expressions\n\nOperators are symbols that perform operations on operands (variables or values).  Expressions are combinations of operators and operands that evaluate to a value.  Understanding operators and expressions is fundamental to writing any program that involves calculations or comparisons.\n\n## 3.1 Arithmetic Operators\n\nThese operators perform basic arithmetic operations:\n\n| Operator | Description       | Example     |\n| --------- | ----------------- | ----------- |\n| `+`       | Addition           | `x + y`     |\n| `-`       | Subtraction        | `x - y`     |\n| `*`       | Multiplication     | `x * y`     |\n| `/`       | Division           | `x / y`     |\n| `//`      | Floor Division     | `x // y`    |\n| `%`       | Modulus (remainder)| `x % y`     |\n| `**`      | Exponentiation     | `x ** y`    |\n\n## 3.2 Comparison Operators\n\nThese operators compare two values and return a boolean result (`True` or `False`):\n\n| Operator | Description          | Example     |\n| --------- | -------------------- | ----------- |\n| `==`      | Equal to             | `x == y`    |\n| `!=`      | Not equal to         | `x != y`    |\n| `>`       | Greater than         | `x > y`     |\n| `<`       | Less than            | `x < y`     |\n| `>=`      | Greater than or equal | `x >= y`    |\n| `<=`      | Less than or equal   | `x <= y`    |\n\n## 3.3 Logical Operators\n\nThese operators combine boolean expressions:\n\n| Operator | Description     | Example       |\n| --------- | ---------------- | -------------- |\n| `and`     | Logical AND      | `x and y`     |\n| `or`      | Logical OR       | `x or y`      |\n| `not`     | Logical NOT      | `not x`       |\n\n## 3.4 Assignment Operators\n\nThese operators assign values to variables:\n\n| Operator | Description          | Example     |\n| --------- | -------------------- | ----------- |\n| `=`       | Assignment           | `x = 10`    |\n| `+=`      | Add and assign      | `x += 5`    |\n| `-=`      | Subtract and assign | `x -= 3`    |\n| `*=`      | Multiply and assign | `x *= 2`    |\n| `/=`      | Divide and assign   | `x /= 4`    |\n\n## 3.5 Operator Precedence\n\nOperator precedence determines the order in which operations are performed.  Parentheses `()` can be used to override precedence.\n\n## Exercises:\n\n1.  Write a program that calculates the area of a circle.\n2.  Write a program that determines if a number is even or odd.\n3.  Write a program that checks if a user is eligible to vote (age >= 18).\n4.  Write a program that converts Celsius to Fahrenheit.\n\n",
                        "resources": [
                            "https://docs.python.org/3/reference/expressions.html",
                            "https://realpython.com/python-operators-expressions/",
                        ],
                    },
                    {
                        "topic_number": 4,
                        "title": "Control Flow: Conditional Statements",
                        "description": "Using `if`, `elif`, and `else` statements to control the flow of execution based on conditions.",
                        "estimated_time_hours": 1.5,
                        "content": '# Control Flow: Conditional Statements\n\nControl flow statements determine the order in which statements are executed in a program.  Conditional statements, using `if`, `elif`, and `else`, allow you to execute different blocks of code based on whether certain conditions are true or false.  This is fundamental to creating programs that can make decisions and respond to different situations.\n\n## 4.1 `if` Statement\n\nThe simplest conditional statement.  If the condition is true, the code block within the `if` statement is executed.\n\n```python\nage = 20\nif age >= 18:\n    print("Eligible to vote")\n```\n\n## 4.2 `if-else` Statement\n\nProvides two possible execution paths.  If the condition is true, the `if` block is executed; otherwise, the `else` block is executed.\n\n```python\nage = 15\nif age >= 18:\n    print("Eligible to vote")\nelse:\n    print("Not eligible to vote")\n```\n\n## 4.3 `if-elif-else` Statement\n\nHandles multiple conditions.  The conditions are evaluated sequentially.  If a condition is true, its corresponding block is executed, and the rest are skipped.  The `else` block (optional) is executed only if none of the conditions are true.\n\n```python\ngrade = 85\nif grade >= 90:\n    print("A")\nelif grade >= 80:\n    print("B")\nelif grade >= 70:\n    print("C")\nelse:\n    print("F")\n```\n\n## 4.4 Nested Conditional Statements\n\nYou can place conditional statements inside other conditional statements to create more complex logic.\n\n```python\nage = 25\nincome = 50000\nif age >= 18:\n    if income >= 40000:\n        print("Eligible for loan")\n    else:\n        print("Income too low for loan")\nelse:\n    print("Too young for loan")\n```\n\n## Exercises:\n\n1. Write a program that determines if a number is positive, negative, or zero.\n2. Write a program that determines the largest of three numbers.\n3. Write a program that calculates the grade based on a score (using if-elif-else).\n4. Create a program that simulates a simple calculator (addition, subtraction, multiplication, division) based on user input.\n\n',
                        "resources": [
                            "https://docs.python.org/3/tutorial/controlflow.html",
                            "https://realpython.com/python-conditional-statements/",
                        ],
                    },
                ],
            },
            {
                "day": 3,
                "total_time_hours": 3,
                "topics": [
                    {
                        "topic_number": 5,
                        "title": "Loops: `for` and `while` loops",
                        "description": "Iterating over sequences and repeating code blocks using `for` and `while` loops.",
                        "estimated_time_hours": 1.5,
                        "content": '# Loops: `for` and `while` loops\n\nLoops are essential for automating repetitive tasks.  Python offers two main types of loops: `for` loops and `while` loops.  `for` loops are generally used for iterating over a sequence (like a list or string), while `while` loops are used for repeating a block of code as long as a condition is true.\n\n## 5.1 `for` Loops\n\n`for` loops iterate over each item in a sequence:\n\n```python\nfruits = ["apple", "banana", "cherry"]\nfor fruit in fruits:\n    print(fruit)\n```\n\nYou can also use `for` loops with `range()` to iterate a specific number of times:\n\n```python\nfor i in range(5):\n    print(i) # Prints 0, 1, 2, 3, 4\n```\n\n## 5.2 `while` Loops\n\n`while` loops execute a block of code as long as a condition is true:\n\n```python\ncount = 0\nwhile count < 5:\n    print(count)\n    count += 1\n```\n\n## 5.3 Loop Control Statements\n\n* **`break`:** Terminates the loop prematurely.\n* **`continue`:** Skips the current iteration and proceeds to the next.\n\n```python\nfor i in range(10):\n    if i == 5:\n        break  # Exit the loop when i is 5\n    print(i)\n\nfor i in range(10):\n    if i == 5:\n        continue # Skip the iteration when i is 5\n    print(i)\n```\n\n## 5.4 Nested Loops\n\nYou can place loops inside other loops to create more complex iterations.  This is often used for processing two-dimensional data structures (like matrices or tables).\n\n```python\nfor i in range(3):\n    for j in range(3):\n        print(f"({i}, {j})")\n```\n\n## Exercises:\n\n1. Write a program that prints the numbers from 1 to 10 using a `for` loop.\n2. Write a program that calculates the sum of numbers from 1 to 100 using a `while` loop.\n3. Write a program that prints the even numbers from 1 to 20 using a `for` loop and a conditional statement.\n4. Write a program that simulates a simple guessing game using a `while` loop and a `break` statement.\n5. Write a program to print multiplication table of a given number using nested loops.\n\n',
                        "resources": [
                            "https://docs.python.org/3/tutorial/controlflow.html#for-statements",
                            "https://docs.python.org/3/tutorial/controlflow.html#while-statements",
                            "https://realpython.com/python-for-loop/",
                        ],
                    },
                    {
                        "topic_number": 6,
                        "title": "Data Structures: Lists and Tuples",
                        "description": "Working with lists (mutable ordered sequences) and tuples (immutable ordered sequences).",
                        "estimated_time_hours": 1.5,
                        "content": '# Data Structures: Lists and Tuples\n\nData structures are ways of organizing and storing data efficiently.  Lists and tuples are two fundamental data structures in Python that are used to store sequences of items.  They are similar in that they are ordered sequences, but they differ in their mutability (ability to be changed after creation).\n\n## 6.1 Lists\n\nLists are mutable, ordered sequences of items.  They are defined using square brackets `[]`.\n\n```python\nmy_list = [1, 2, 3, "apple", "banana"]\n```\n\n**List Operations:**\n\n* **Access elements:** `my_list[0]` (accesses the first element)\n* **Slicing:** `my_list[1:3]` (accesses elements from index 1 to 2)\n* **Append:** `my_list.append(4)` (adds 4 to the end)\n* **Insert:** `my_list.insert(2, "orange")` (inserts "orange" at index 2)\n* **Remove:** `my_list.remove("apple")` (removes the first occurrence of "apple")\n* **Pop:** `my_list.pop()` (removes and returns the last element)\n* **Length:** `len(my_list)` (returns the number of elements)\n\n## 6.2 Tuples\n\nTuples are immutable, ordered sequences of items.  They are defined using parentheses `()`.\n\n```python\nmy_tuple = (1, 2, 3, "apple", "banana")\n```\n\nBecause tuples are immutable, you cannot modify them after creation (you can\'t append, insert, remove elements).  However, you can still access elements using indexing and slicing, just like with lists.\n\n## 6.3 Choosing Between Lists and Tuples\n\n* Use lists when you need a mutable sequence (you\'ll need to add, remove, or change elements).\n* Use tuples when you need an immutable sequence (the data should not be changed after creation).  Tuples are generally slightly more memory-efficient than lists.\n\n## Exercises:\n\n1. Create a list of your favorite colors and print each color.\n2. Create a tuple of your favorite fruits and print the number of fruits.\n3. Create a list of numbers, sort it in ascending order, and print the sorted list.\n4. Create a list of strings, reverse it, and print the reversed list.\n5.  Create a tuple containing a list.  Try modifying the list within the tuple. What happens? Explain why.\n\n',
                        "resources": [
                            "https://docs.python.org/3/tutorial/datastructures.html",
                            "https://realpython.com/python-lists-tuples/",
                        ],
                    },
                ],
            },
        ],
    },
]
